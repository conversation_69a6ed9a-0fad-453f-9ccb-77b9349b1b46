package exchange.spot.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.service.CurrencyPairConfigService;
import lombok.RequiredArgsConstructor;

@SpringBootTest(classes = {exchange.app.Application.class})
@RequiredArgsConstructor(onConstructor = @__({@Autowired}))
@AutoConfigureWebTestClient
@ActiveProfiles({"local"})
public class CurrencyPairConfigServiceTest {

  private final CurrencyPairConfigService currencyPairConfigService;

  @Test
  @DisplayName("追加修正: MAX_ACTIVE_ORDER_AMOUNT")
  public void findByConditionTest() {

    CurrencyPairConfig currencyPairConfig = currencyPairConfigService.findByCondition(TradeType.SPOT, CurrencyPair.ADA_JPY);
    assertNotNull(currencyPairConfig.getMaxOrderAmount());
  }
}

package exchange.admin.controller;

import exchange.admin.entity.AdminUser;
import exchange.admin.model.response.ReportCustomerDepositStatement;
import exchange.admin.model.response.ReportCustomerDepositStatement.NewAssetSummary;
import exchange.common.component.DataSourceManager;
import exchange.common.constant.Authority;
import exchange.common.constant.Currency;
import exchange.common.entity.AbstractEntity;
import exchange.common.entity.User;
import exchange.common.model.response.ReportText;
import exchange.common.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report_customer_deposit")
public class V1ReportCustomerDepositStatement extends ExchangeAdminController {

	private final DataSourceManager dataSourceManager;
	private final TemplateEngine templateEngine;
	private static final String DAIRY_REPORT_TEMPLATE = "report/reportcustomerdeposit.txt";
	private final UserService userService;

	@GetMapping()
	public ResponseEntity<ReportText> download(HttpServletResponse response, @AuthenticationPrincipal AdminUser adminUser,
											   @RequestParam(value = "dateFrom", required = false) Long dateFrom) throws Exception {
		// 有効なユーザを全て取得する
		final var users = userService.findAll()
				.stream()
				.filter(user -> user.getUserInfo() != null || user.getUserInfoCorporate() != null)
				.toList();

		// ユーザをMAP化する
		final var userMap = users
				.stream()
				.collect(Collectors.toMap(AbstractEntity::getId, it -> it));

		String sql = """
		    SELECT
				user_id,
				'' AS first_name,
				'' AS last_name,
				MAX( CASE WHEN currency = "JPY" THEN currency ELSE '' END ) AS "jpy_currency",
				MAX( CASE WHEN currency = "JPY" THEN current_amount ELSE '' END ) AS "jpy_current_amount",
				MAX( CASE WHEN currency = "ADA" THEN currency ELSE '' END ) AS "ada_currency",
				MAX( CASE WHEN currency = "ADA" THEN current_amount ELSE '' END ) AS "ada_current_amounty",
				MAX( CASE WHEN currency = "ADA" THEN currency ELSE '' END ) AS "ada_staking_lock",
				MAX( CASE WHEN currency = "ADA" THEN staking_lock_amount ELSE '' END ) AS "ada_staking_lock_amount",
				MAX( CASE WHEN currency = "NIDT" THEN currency ELSE '' END ) AS "nidt_currency",
				MAX( CASE WHEN currency = "NIDT" THEN current_amount ELSE '' END ) AS "nidt_current_amount",
				MAX( CASE WHEN currency = "BTC" THEN currency ELSE '' END ) AS "btc_currency",
				MAX( CASE WHEN currency = "BTC" THEN current_amount ELSE '' END ) AS "btc_current_amounty",
				MAX( CASE WHEN currency = "ETH" THEN currency ELSE '' END ) AS "eth_currency",
				MAX( CASE WHEN currency = "ETH" THEN current_amount ELSE '' END ) AS "eth_current_amount",
				MAX( CASE WHEN currency = "XRP" THEN currency ELSE '' END ) AS "xrp_currency",
				MAX( CASE WHEN currency = "XRP" THEN current_amount ELSE '' END ) AS "xrp_current_amount",
                MAX( CASE WHEN currency = "ETH" THEN currency ELSE '' END ) AS "eth_staking_lock",
                MAX( CASE WHEN currency = "ETH" THEN staking_lock_amount ELSE '' END ) AS "eth_staking_lock_amount"
			FROM
				(
				SELECT
					asset_summary.user_id AS user_id,
					asset_summary.currency AS currency,
					asset_summary.current_amount AS current_amount,
					asset_summary.staking_lock_amount_sum AS staking_lock_amount
				FROM
					asset_summary
				WHERE
					1 = 1 
					AND asset_summary.target_at > :from
					AND asset_summary.target_at <= :to 
				) AS new_asset_summary 
			GROUP BY
				user_id
        		    """;
		
	    // 自社口座を全て取得する
	    final var systemUsers = userService.findByInsideAccountFlg(true)
	            .stream()
	            .filter(user -> user.getUserInfo() != null || user.getUserInfoCorporate() != null)
	            .toList();
	    String ownerIds[] = systemUsers.stream().map((owner) -> {
	      return owner.getId().toString();
	    }).collect(Collectors.joining(",")).split(",");
		
		EntityManager entityManager =
	            dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
		ReportText report = new ReportText();
		NumberFormat numberFormat = NumberFormat.getNumberInstance();
		try {
			Query query = entityManager.createNativeQuery(sql);
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withLocale(Locale.JAPAN)
					.withZone(ZoneId.systemDefault());
			Date date = new Date();
			query.setParameter("from", dateFrom != null ? Instant.ofEpochMilli(dateFrom).minus(1,ChronoUnit.DAYS) : formatter.format(Instant.ofEpochMilli(date.getTime()).minus(1,ChronoUnit.DAYS)));
			query.setParameter("to", dateFrom != null ? Instant.ofEpochMilli(dateFrom): formatter.format(Instant.ofEpochMilli(date.getTime())));

			@SuppressWarnings("unchecked")
			List<Object[]> list = query.getResultList();
			ReportCustomerDepositStatement reportCustomerDepositStatement = new ReportCustomerDepositStatement();
			List<NewAssetSummary> newAssetSummaryList = new ArrayList<>();
			BigDecimal balance = new BigDecimal(0.0);
			BigDecimal adaamount = new BigDecimal(0.0);
			BigDecimal nidtamount = new BigDecimal(0.0);
			BigDecimal applyadaamount = new BigDecimal(0.0);
			BigDecimal btcamount = new BigDecimal(0.0);
			BigDecimal ethamount = new BigDecimal(0.0);
			BigDecimal xrpamount = new BigDecimal(0.0);
	        BigDecimal applyethamount = new BigDecimal(0.0);
			for (Object[] obj : list) {
				NewAssetSummary newAssetSummary = new NewAssetSummary();
				newAssetSummary.setUserid(String.valueOf(obj[0]));
				// 取引した顧客
				User transactionUser = userMap.get(Long.valueOf(String.valueOf(obj[0])));
				String userName = transactionUser == null ? "": transactionUser.getAuthorities().get(0).getAuthority().equals(Authority.PERSONAL.name()) ?
						transactionUser.getUserInfo().getLastName() + " " + transactionUser.getUserInfo().getFirstName() :
						transactionUser.getUserInfoCorporate().getName(); // 取引した顧客の氏名
				newAssetSummary.setUsername(userName);

				if (((String) obj[4]).isEmpty()) {
					newAssetSummary.setBalance(BigDecimal.ZERO.toPlainString());
				} else {
					balance = new BigDecimal((String)obj[4]);
					newAssetSummary.setBalance(Currency.JPY.getScaledAmount(balance, RoundingMode.FLOOR).toPlainString());
				}
				if (((String) obj[6]).isEmpty()) {
					newAssetSummary.setAdaamount(BigDecimal.ZERO.toPlainString());
				} else {
					adaamount = new BigDecimal((String)obj[6]);
					newAssetSummary.setAdaamount(Currency.ADA.getScaledAmount(adaamount, RoundingMode.FLOOR).toPlainString());
				}
				if (((String) obj[8]).isEmpty()) {
					newAssetSummary.setApplyamount(BigDecimal.ZERO.toPlainString());
				} else {
					applyadaamount = new BigDecimal((String)obj[8]);
					newAssetSummary.setApplyamount(Currency.ADA.getScaledAmount(applyadaamount, RoundingMode.FLOOR).toPlainString());
				}
				if (((String) obj[10]).isEmpty()) {
					newAssetSummary.setNidtamount(BigDecimal.ZERO.toPlainString());
				} else {
					nidtamount = new BigDecimal((String)obj[10]);
					newAssetSummary.setNidtamount(Currency.NIDT.getScaledAmount(nidtamount, RoundingMode.FLOOR).toPlainString());
				}

				if (((String) obj[12]).isEmpty()) {
					newAssetSummary.setBtcamount(BigDecimal.ZERO.toPlainString());
				} else {
					btcamount = new BigDecimal((String)obj[12]);
					newAssetSummary.setBtcamount(Currency.BTC.getScaledAmount(btcamount, RoundingMode.FLOOR).toPlainString());
				}
				if (((String) obj[14]).isEmpty()) {
					newAssetSummary.setEthamount(BigDecimal.ZERO.toPlainString());
				} else {
					ethamount = new BigDecimal((String)obj[14]);
					newAssetSummary.setEthamount(Currency.ETH.getScaledAmount(ethamount, RoundingMode.FLOOR).toPlainString());
				}
				if (((String) obj[16]).isEmpty()) {
					newAssetSummary.setXrpamount(BigDecimal.ZERO.toPlainString());
				} else {
					xrpamount = new BigDecimal((String)obj[16]);
					newAssetSummary.setXrpamount(Currency.XRP.getScaledAmount(xrpamount, RoundingMode.FLOOR).toPlainString());
				}
	            if (((String) obj[18]).isEmpty()) {
	                newAssetSummary.setApplyethamount(BigDecimal.ZERO.toPlainString());
	            } else {
	                applyethamount = new BigDecimal((String)obj[18]);
	                newAssetSummary.setApplyethamount(Currency.ETH.getScaledAmount(applyethamount, RoundingMode.FLOOR).toPlainString());
	            }
				newAssetSummaryList.add(newAssetSummary);
			}
			
			List<NewAssetSummary> newAssetSummaryFilterList = new ArrayList<>();
	        newAssetSummaryFilterList.addAll(newAssetSummaryList
	        		.stream()
	        		.filter(newAssetSummaryLists -> !Arrays.asList(ownerIds).contains(newAssetSummaryLists.getUserid().toString()))
	        		.toList());

	        List<NewAssetSummary> newAssetSummaryFilterListString = new ArrayList<>();
	        for (int i = 0;i < newAssetSummaryFilterList.size(); i++) {
	        	NewAssetSummary newAssetSummaryString = new NewAssetSummary();
	        	newAssetSummaryString.setUserid(newAssetSummaryFilterList.get(i).getUserid());
	        	newAssetSummaryString.setUsername(newAssetSummaryFilterList.get(i).getUsername());
	        	newAssetSummaryString.setBalance(toStrigForReport(Currency.JPY, new BigDecimal(newAssetSummaryFilterList.get(i).getBalance()), numberFormat));
	        	newAssetSummaryString.setAdaamount(toStrigForReport(Currency.ADA, new BigDecimal(newAssetSummaryFilterList.get(i).getAdaamount()), numberFormat));
	        	newAssetSummaryString.setApplyamount(toStrigForReport(Currency.ADA, new BigDecimal(newAssetSummaryFilterList.get(i).getApplyamount()), numberFormat));
	        	newAssetSummaryString.setNidtamount(toStrigForReport(Currency.NIDT, new BigDecimal(newAssetSummaryFilterList.get(i).getNidtamount()), numberFormat));
	        	newAssetSummaryString.setBtcamount(toStrigForReport(Currency.BTC, new BigDecimal(newAssetSummaryFilterList.get(i).getBtcamount()), numberFormat));
	        	newAssetSummaryString.setEthamount(toStrigForReport(Currency.ETH, new BigDecimal(newAssetSummaryFilterList.get(i).getEthamount()), numberFormat));
	        	newAssetSummaryString.setApplyethamount(toStrigForReport(Currency.ETH, new BigDecimal(newAssetSummaryFilterList.get(i).getApplyethamount()), numberFormat));
	        	newAssetSummaryString.setXrpamount(toStrigForReport(Currency.XRP, new BigDecimal(newAssetSummaryFilterList.get(i).getXrpamount()), numberFormat));
	        	newAssetSummaryFilterListString.add(newAssetSummaryString);
	        }
	        
			reportCustomerDepositStatement.setNewAssetSummaryList(newAssetSummaryFilterListString);
			BigDecimal balancetotal = new BigDecimal(0.0);
			BigDecimal adaamounttotal = new BigDecimal(0.0);
			BigDecimal nidtamounttotal = new BigDecimal(0.0);
			BigDecimal btcamounttotal = new BigDecimal(0.0);
			BigDecimal ethamounttotal = new BigDecimal(0.0);
			BigDecimal xrpamounttotal = new BigDecimal(0.0);

			for (int i = 0; i < newAssetSummaryFilterList.size(); i++) {
				balancetotal = balancetotal.add(new BigDecimal(newAssetSummaryFilterList.get(i).getBalance()));
				adaamounttotal = adaamounttotal.add(new BigDecimal(newAssetSummaryFilterList.get(i).getAdaamount()));
				nidtamounttotal = nidtamounttotal.add(new BigDecimal(newAssetSummaryFilterList.get(i).getNidtamount()));
				btcamounttotal = btcamounttotal.add(new BigDecimal(newAssetSummaryFilterList.get(i).getBtcamount()));
				ethamounttotal = ethamounttotal.add(new BigDecimal(newAssetSummaryFilterList.get(i).getEthamount()));
				xrpamounttotal = xrpamounttotal.add(new BigDecimal(newAssetSummaryFilterList.get(i).getXrpamount()));
			}
			
			reportCustomerDepositStatement.setBtcamounttotal(toStrigForReport(Currency.BTC, btcamounttotal, numberFormat));
			reportCustomerDepositStatement.setXrpamounttotal(toStrigForReport(Currency.XRP, xrpamounttotal, numberFormat));
			reportCustomerDepositStatement.setBalancetotal(toStrigForReport(Currency.JPY, balancetotal, numberFormat));
	        reportCustomerDepositStatement.setAdaamounttotal(toStrigForReport(Currency.ADA, adaamounttotal, numberFormat));
	        reportCustomerDepositStatement.setNidtamounttotal(toStrigForReport(Currency.NIDT, nidtamounttotal, numberFormat));
	        reportCustomerDepositStatement.setEthamounttotal(toStrigForReport(Currency.ETH, ethamounttotal, numberFormat));

			final Context ctx = new Context(Locale.getDefault());
			ctx.setVariable("report", reportCustomerDepositStatement);
			String reportText = this.templateEngine.process(DAIRY_REPORT_TEMPLATE, ctx);
			report.setText(reportText);
		}finally {
	        entityManager.clear();
	        entityManager.close();
	      }
		
		return ResponseEntity.ok(report);
	}
	
	private static String toStrigForReport(Currency currency, BigDecimal value, NumberFormat numberFormat) {
  	  // stripTrailingZeros() 末尾0除去
  	  // toPlainString() 指数表記にならないようにString変換
  	  String strValue = currency.getScaledAmount(value, RoundingMode.FLOOR).stripTrailingZeros().toPlainString();
        // 整数部3桁区切り
  	  int decimalPointIndex = strValue.indexOf(".");
  	  if (decimalPointIndex < 0) {
  	    // 小数点なし
  	    return numberFormat.format(Long.valueOf(strValue));
  	  } else {
  	    // 小数点あり
  	    String seisu = strValue.substring(0, decimalPointIndex);
  	    return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
  	  }
  	}
}


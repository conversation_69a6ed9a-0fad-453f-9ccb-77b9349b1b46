package exchange.admin.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.admin.model.request.SpotCoverOrderUpdateForm;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeType;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.Symbol;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.model.response.SpotCopyTradeSumCoveredData;
import exchange.common.model.response.SpotCoverOrderTableData;
import exchange.common.model.response.SpotCoverOrderWithTradesData;
import exchange.common.model.response.SpotCoverProfitData;
import exchange.common.service.SymbolService;
import exchange.common.util.DateUnit;
import exchange.spot.entity.SpotCopyTrade;
import exchange.spot.entity.SpotCoverOrder;
import exchange.spot.service.SpotCopyTradeService;
import exchange.spot.service.SpotCoverOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/spot/cover-order")
public class V1SpotCoverOrderRestController extends ExchangeAdminController {

  private final SymbolService symbolService;

  // adminでは無効な通貨ペア(currencyPairConfig.enabled=false)も処理対象とするため
  // paramのsymbolIdの有効性チェック不要

  @GetMapping
  public ResponseEntity<SpotCoverOrderTableData> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "symbolId", required = true) Long symbolId,
      @RequestParam(value = "id", required = true) Long id)
      throws CustomException {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    Symbol symbol = symbolService.findOne(symbolId);
    if (symbol == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }

    try {
      SpotCoverOrder spotCoverOrder = SpotCoverOrderService.getBean(symbol).findOne(id);
      if (spotCoverOrder == null) {
        throw new CustomException(ErrorCode.DEALING_ERROR_COVER_ORDER_NOT_FOUND);
      }
      return ResponseEntity.ok(
          new SpotCoverOrderTableData().setProperties(symbol.getCurrencyPair(), spotCoverOrder));
    } catch(NoSuchBeanDefinitionException e) {
      throw new CustomException(ErrorCode.DEALING_ERROR_COVER_ORDER_NOT_FOUND);
    }
  }

  @GetMapping("/page")
  public ResponseEntity<PageData<SpotCoverOrderTableData>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "idFrom", required = false) Long idFrom,
      @RequestParam(value = "idTo", required = false) Long idTo,
      @RequestParam(value = "currencyPair", required = true) CurrencyPair currencyPair,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "orderType", required = false) OrderType orderType,
      @RequestParam(value = "orderSide", required = false) OrderSide orderSide,
      @RequestParam(value = "exchange", required = true) Exchange exchange,
      @RequestParam(value = "exchangeOrderId", required = false) Long exchangeOrderId,
      @RequestParam(value = "greaterThanRemainingAmount",required = false) BigDecimal greaterThanRemainingAmount,
      @RequestParam(value = "lessThanRemainingAmount",required = false) BigDecimal lessThanRemainingAmount,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    //dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
    PageData<SpotCoverOrderTableData> pg =
        new PageData<SpotCoverOrderTableData>(number, size, 0, null);
    List<SpotCoverOrderTableData> tableDatas = new ArrayList<SpotCoverOrderTableData>();

    // 注文一覧のため、件数上通貨ペア必須とする
    Symbol symbol = symbolService.findByCondition(TradeType.SPOT, currencyPair);
    if (symbol == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }

    /** カバー注文取得 */
    PageData<SpotCoverOrder> pageSpotCoverOrders =
        SpotCoverOrderService.getBean(symbol)
            .findByConditionPageData(
                symbol.getId(),
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo,
                orderType,
                orderSide,
                exchange,
                exchangeOrderId,
                greaterThanRemainingAmount,
                lessThanRemainingAmount,
                number,
                size);

    //    log.info(
    //        "DBMITO,admin,spotCoverOrderGetLog,symbolId,"
    //            + symbol.getId()
    //            + ",spotCoverOrders,"
    //            + JsonUtil.encode(spotCoverOrders));
    List<SpotCoverOrder> spotCoverOrders = pageSpotCoverOrders.getContent();
    if (CollectionUtils.isEmpty(spotCoverOrders)) {
      return ResponseEntity.ok(pg);
    }

    /** カバー注文に紐づくのコピー注文約定履歴を取得 */
    // spotCopyTradeのcoverOrderIdにはカバー注文発注先の注文IDが設定される
    // カバー注文発注先の注文IDはexchangeOrderIdに保持
    List<SpotCopyTrade> spotCopyTrades =
        SpotCopyTradeService.getBean(symbol)
            .findAllByCondition(
                symbol.getId(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                spotCoverOrders.stream()
                    .map(SpotCoverOrder::getExchangeOrderId)
                    .collect(Collectors.toList()),
                false);

    //    log.info(
    //        "DBMITO,admin,spotCoverOrderGetLog,symbolId,"
    //            + symbol.getId()
    //            + ",spotCopyTrades,"
    //            + JsonUtil.encode(spotCopyTrades));

    if (CollectionUtils.isEmpty(spotCopyTrades)) {
      return ResponseEntity.ok(pg);
    }

    /** カバー注文とコピー注文約定履歴のkeyファイル作成 */
    for (SpotCoverOrder spotCoverOrder : spotCoverOrders) {
      SpotCoverOrderWithTradesData keyData =
          new SpotCoverOrderWithTradesData()
              .setProperties(
                  spotCoverOrder,
                  spotCopyTrades.stream()
                      .filter(
                          trade ->
                              trade.getCoverOrderId().equals(spotCoverOrder.getExchangeOrderId()))
                      .collect(Collectors.toList()));

      /** コピー注文約定履歴をサマリしてresponseを編集 */
      SpotCoverOrderTableData tableData =
          new SpotCoverOrderTableData().setProperties(currencyPair, spotCoverOrder);
      tableData.setCopyTradeSellAmount(
          keyData.getSpotCopyTrades().stream()
              .filter(trade -> trade.getOrderSide() == OrderSide.SELL)
              .map(SpotCopyTrade::getAmount)
              .reduce(BigDecimal.ZERO, BigDecimal::add));
      tableData.setCopyTradeBuyAmount(
          keyData.getSpotCopyTrades().stream()
              .filter(trade -> trade.getOrderSide() == OrderSide.BUY)
              .map(SpotCopyTrade::getAmount)
              .reduce(BigDecimal.ZERO, BigDecimal::add));
      // 損益計算のため桁数処理しない
      tableData.setCopyTradeAveragePrice(
          SpotCopyTradeService.getBean(symbol)
              .getAverageCopyTradePrice(keyData.getSpotCopyTrades()));
      tableData.setCopyTradeFeeSum(
          keyData.getSpotCopyTrades().stream()
              .map(SpotCopyTrade::getFee)
              .reduce(BigDecimal.ZERO, BigDecimal::add));

      // 売り価格 - 買い価格(カバーが売りの場合カバー価格 - コピー約定価格)
      // 損益検証用に桁数処理しない(別途検討)
      BigDecimal priceDiff =
          (spotCoverOrder.getOrderSide() == OrderSide.SELL)
              ? tableData.getAveragePrice().subtract(tableData.getCopyTradeAveragePrice())
              : tableData.getCopyTradeAveragePrice().subtract(tableData.getAveragePrice());
      tableData.setPriceDiff(priceDiff);
      //      tableData.setProfit(
      //          SpotTradeService.getBean(symbol)
      //              .calculateAssetAmount(
      //                  symbol, tableData.getFilledAmount().multiply(tableData.getPriceDiff())));

      // 損益検証用に桁数処理しない(別途検討)
      tableData.setProfit(tableData.getFilledAmount().multiply(tableData.getPriceDiff()));

      tableDatas.add(tableData);
    }

    return CollectionUtils.isEmpty(tableDatas)
        ? ResponseEntity.ok(pg)
        : ResponseEntity.ok(
            new PageData<SpotCoverOrderTableData>(number, size, pageSpotCoverOrders.getTotalElements(), tableDatas));
  }

  @GetMapping("/profit")
  public ResponseEntity<PageData<SpotCoverProfitData>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "currencyPair", required = false) CurrencyPair currencyPair,
      @RequestParam(value = "exchange", required = true) Exchange exchange,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

    // 概要
    //  カバー注文の約定総額（約定価格*約定数量)と、
    // 該当するコピー注文約定履歴の約定総額(加重平均価格*カバー注文約定数量) の売買差を損益概算とする
    PageData<SpotCoverProfitData> pg = new PageData<SpotCoverProfitData>(number, size, 0, null);
    List<SpotCoverProfitData> spotCoverProfitDataList = new ArrayList<SpotCoverProfitData>();

    List<Symbol> symbols = symbolService.findAllListByCondition(TradeType.SPOT, currencyPair);
    if (CollectionUtils.isEmpty(symbols)) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }

    for (Symbol symbol : symbols) {
      /** 約定済みカバー注文取得 */
      List<SpotCoverOrder> spotCoverOrdersAll =
          SpotCoverOrderService.getBean(symbol)
              .findByCondition(
                  symbol.getId(),
                  null,
                  null,
                  null,
                  dateFrom,
                  dateTo,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  0,
                  Integer.MAX_VALUE,
                  false);

      //      log.info(
      //          "DBMITO,admin,spotCoverOrderProfitLog,symbolId,"
      //              + symbol.getId()
      //              + ",spotCoverOrdersAll,"
      //              + JsonUtil.encode(spotCoverOrdersAll));

      if (CollectionUtils.isEmpty(spotCoverOrdersAll)) {
        continue;
      }

      // 約定数量 > 0
      List<SpotCoverOrder> spotCoverOrders =
          spotCoverOrdersAll.stream()
              .filter(order -> order.getAmount().compareTo(order.getRemainingAmount()) > 0)
              .collect(Collectors.toList());

      //      log.info(
      //          "DBMITO,admin,spotCoverOrderProfitLog,symbolId,"
      //              + symbol.getId()
      //              + ",spotCoverOrders,"
      //              + JsonUtil.encode(spotCoverOrders));

      if (CollectionUtils.isEmpty(spotCoverOrders)) {
        continue;
      }

      // spotCopyTradeのcoverOrderIdにはカバー注文発注先の注文IDが設定される
      // カバー注文発注先の注文IDはexchangeOrderIdに保持
      List<Long> coverOrderIds =
          spotCoverOrders.stream()
              .map(SpotCoverOrder::getExchangeOrderId)
              .collect(Collectors.toList());

      //      log.info(
      //          "DBMITO,admin,spotCoverOrderProfitLog,symbolId,"
      //              + symbol.getId()
      //              + ",coverOrderIds,"
      //              + JsonUtil.encode(coverOrderIds));

      // カバー済のコピー注文約定履歴を取得
      List<SpotCopyTrade> spotCopyTrades =
          SpotCopyTradeService.getBean(symbol)
              .findAllByCondition(
                  symbol.getId(),
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  null,
                  coverOrderIds,
                  false);

      //      log.info(
      //          "DBMITO,admin,spotCoverOrderProfitLog,symbolId,"
      //              + symbol.getId()
      //              + ",spotCopyTrades,"
      //              + JsonUtil.encode(spotCopyTrades));

      if (CollectionUtils.isEmpty(spotCopyTrades)) {
        continue;
      }

      List<SpotCopyTradeSumCoveredData> sumList = new ArrayList<SpotCopyTradeSumCoveredData>();
      for (SpotCoverOrder spotCoverOrder : spotCoverOrders) {
        SpotCopyTradeSumCoveredData spotCopyTradeSumCoveredData =
            new SpotCopyTradeSumCoveredData()
                .setProperties(
                    symbol.getId(),
                    symbol.getCurrencyPair(),
                    spotCoverOrder.getExchangeOrderId(),
                    spotCoverOrder.getOrderSide(),
                    spotCoverOrder.getAmount(),
                    // 損益計算用に桁数処理行わない
                    spotCoverOrder.getAveragePrice(),
                    spotCoverOrder.getAmount().subtract(spotCoverOrder.getRemainingAmount()),
                    spotCoverOrder.getFee(),
                    spotCopyTrades.stream()
                        .filter(
                            trade ->
                                trade.getCoverOrderId().equals(spotCoverOrder.getExchangeOrderId()))
                        .collect(Collectors.toList()));

        //        spotCopyTradeSumCoveredData.setCopyTradeAveragePrice(
        //            symbol
        //                .getCurrencyPair()
        //                .getScaledPrice(
        //                    SpotCopyTradeService.getBean(symbol)
        //                        .getAverageCopyTradePrice(
        //                            spotCopyTradeSumCoveredData.getSpotCopyTrades())));

        // 損益計算用に桁数処理行わない
        spotCopyTradeSumCoveredData.setCopyTradeAveragePrice(
            SpotCopyTradeService.getBean(symbol)
                .getAverageCopyTradePrice(spotCopyTradeSumCoveredData.getSpotCopyTrades()));

        // 売り価格 - 買い価格(カバーが売りの場合カバー価格 - コピー約定価格)
        // 損益計算用に、coverAveragePrice, copyTradeAveragePriceは桁数処理行わない
        BigDecimal priceDiff =
            (spotCopyTradeSumCoveredData.getCoverOrderSide() == OrderSide.SELL)
                ? spotCopyTradeSumCoveredData
                    .getCoverAveragePrice()
                    .subtract(spotCopyTradeSumCoveredData.getCopyTradeAveragePrice())
                : spotCopyTradeSumCoveredData
                    .getCopyTradeAveragePrice()
                    .subtract(spotCopyTradeSumCoveredData.getCoverAveragePrice());
        spotCopyTradeSumCoveredData.setPriceDiff(priceDiff);
        spotCopyTradeSumCoveredData.setCopyTradeAmountSum(
            spotCopyTradeSumCoveredData.getSpotCopyTrades().stream()
                .map(SpotCopyTrade::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        spotCopyTradeSumCoveredData.setCopyTradeFeeSum(
            spotCopyTradeSumCoveredData.getSpotCopyTrades().stream()
                .map(SpotCopyTrade::getFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        //        log.info(
        //            "DBMITO,admin,spotCoverOrderProfitLog,symbolId,"
        //                + symbol.getId()
        //                + ",spotCopyTradeSumCoveredData,"
        //                + JsonUtil.encode(spotCopyTradeSumCoveredData));

        sumList.add(spotCopyTradeSumCoveredData);
      }

      spotCoverProfitDataList.add(
          new SpotCoverProfitData()
              .setProperties(
                  exchange,
                  symbol.getId(),
                  symbol.getCurrencyPair(),
                  sumList.stream()
                      .map(SpotCopyTradeSumCoveredData::getCoverAmount)
                      .reduce(BigDecimal.ZERO, BigDecimal::add),
                  sumList.stream()
                      .map(SpotCopyTradeSumCoveredData::getCoverFilledAmount)
                      .reduce(BigDecimal.ZERO, BigDecimal::add),
                  // 損益検証用に桁数処理行わない
                  // 損益は手数料含まない
                  sumList.stream()
                      .map(data -> data.getPriceDiff().multiply(data.getCoverFilledAmount()))
                      .reduce(BigDecimal.ZERO, BigDecimal::add),
                  sumList.stream()
                      .map(SpotCopyTradeSumCoveredData::getCoverFee)
                      .reduce(BigDecimal.ZERO, BigDecimal::add),
                  sumList.stream()
                      .map(SpotCopyTradeSumCoveredData::getCopyTradeAmountSum)
                      .reduce(BigDecimal.ZERO, BigDecimal::add),
                  sumList.stream()
                      .map(SpotCopyTradeSumCoveredData::getCopyTradeFeeSum)
                      .reduce(BigDecimal.ZERO, BigDecimal::add)));

      //      SpotTradeService.getBean(symbol)
      //      .calculateAssetAmount(
      //          symbol,
      //          sumList
      //              .stream()
      //              .map(
      //                  data -> data.getPriceDiff().multiply(data.getCoverFilledAmount()))
      //              .reduce(BigDecimal.ZERO, BigDecimal::add)),
    }

    return CollectionUtils.isEmpty(spotCoverProfitDataList)
        ? ResponseEntity.ok(pg)
        : ResponseEntity.ok(
            new PageData<SpotCoverProfitData>()
                .createPageData(
                    number, size, spotCoverProfitDataList.size(), spotCoverProfitDataList));
  }

  @PutMapping
  public ResponseEntity<SpotCoverOrderTableData> update(
      @AuthenticationPrincipal AdminUser adminUser,
      @Valid @RequestBody SpotCoverOrderUpdateForm form)
      throws CustomException {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    Symbol symbol = symbolService.findOne(form.getSymbolId());
    if (symbol == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }

    SpotCoverOrder spotCoverOrder = SpotCoverOrderService.getBean(symbol).findOne(form.getId());
    if (spotCoverOrder == null) {
      throw new CustomException(ErrorCode.DEALING_ERROR_COVER_ORDER_NOT_FOUND);
    }

    spotCoverOrder.setPrice(form.getPrice());
    spotCoverOrder.setAveragePrice(form.getAveragePrice());
    spotCoverOrder.setAmount(form.getAmount());
    spotCoverOrder.setRemainingAmount(form.getRemainingAmount());
    spotCoverOrder.setFee(form.getFee());

    return ResponseEntity.ok(
        new SpotCoverOrderTableData()
            .setProperties(
                symbol.getCurrencyPair(),
                SpotCoverOrderService.getBean(symbol).save(spotCoverOrder)));
  }

  @DeleteMapping
  public ResponseEntity<SpotCoverOrder> delete(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "id", required = true) Long id,
      @RequestParam(value = "symbolId", required = true) Long symbolId)
      throws Exception {

    log.info("DBMITO,admin,spotCoverOrderGetLog,symbolId," + symbolId + ",delete start");

    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    Symbol symbol = symbolService.findOne(symbolId);
    if (symbol == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }

    // 削除対象カバー注文取得
    SpotCoverOrder spotCoverOrder = SpotCoverOrderService.getBean(symbol).findOne(id);

    if (spotCoverOrder == null) {
      throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
    }

    // 削除対象カバー注文のカバー対象となるコピー注文約定履歴を取得
    List<SpotCopyTrade> spotCopyTrades =
        SpotCopyTradeService.getBean(symbol)
            .findAllByCondition(
                symbol.getId(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                spotCoverOrder.getExchangeOrderId(),
                null,
                null);

    // 一連の複数件更新で1コミット
    customTransactionManager.execute(
        entityManager -> {
          // カバー注文削除
          SpotCoverOrderService.getBean(symbol).delete(spotCoverOrder, entityManager);
          // カバー注文に紐づくコピー注文約定履歴を未カバーに戻す
          for (SpotCopyTrade spotCopyTrade : spotCopyTrades) {
            spotCopyTrade.setCoverOrderId(null);
            SpotCopyTradeService.getBean(symbol).save(spotCopyTrade, entityManager);
          }
        });

    return ResponseEntity.ok(spotCoverOrder);
  }
}

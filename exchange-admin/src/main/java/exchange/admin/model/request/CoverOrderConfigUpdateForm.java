package exchange.admin.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;

import exchange.common.constant.TradeType;
import lombok.Getter;
import lombok.Setter;

public class CoverOrderConfigUpdateForm {

  @Getter @Setter @NotNull private Long id;

  @Getter @Setter private BigDecimal rangePricePercent;

  @Getter @Setter @NotNull private BigDecimal minOrderAmount;

  @Getter @Setter @NotNull private boolean enabled;

  @Getter @Setter private TradeType tradeType;

  @Getter @Setter private BigDecimal orderAmountPercent;

}

package exchange.spot.entity;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "spot_cover_order_ada_jpy")
@ToString(callSuper = true, doNotUseGetters = true)
public class SpotCoverOrderAdaJpy extends SpotCoverOrder {

  private static final long serialVersionUID = -8375661052393424873L;
}

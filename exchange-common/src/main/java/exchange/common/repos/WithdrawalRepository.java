package exchange.common.repos;

import java.math.BigDecimal;
import java.util.Date;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import exchange.common.entity.Withdrawal;

/**
 * @author: wen.y
 * @date: 2024/8/12
 */
public interface WithdrawalRepository extends JpaRepository<Withdrawal,Long> {

	@Modifying
	@Query("UPDATE Withdrawal w SET w.chainalysisExternalId = :chainalysisExternalId,w.updatedAt = :updatedAt WHERE w.id = :id")
	@Transactional
	int updateByIdAndChainalysisExternalId(@Param("id") Long id, @Param("chainalysisExternalId") String chainalysisExternalId, @Param("updatedAt") Date updatedAt);
	
	@Modifying
    @Query("UPDATE Withdrawal w SET w.jpyConversion = :jpyConversion WHERE w.id = :id")
    @Transactional
    int updateById(@Param("id") Long id, @Param("jpyConversion") BigDecimal jpyConversion);
}

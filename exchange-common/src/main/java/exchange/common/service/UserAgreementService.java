package exchange.common.service;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.Predicate;

import exchange.common.model.dto.UserAgreementDTO;
import exchange.common.repos.UserAgreementRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.UserAgreementType;

import exchange.common.entity.UserAgreement;
import exchange.common.entity.UserAgreement_;
import exchange.common.predicate.UserAgreementPredicate;

@Service
@RequiredArgsConstructor
public class UserAgreementService extends EntityService<UserAgreement, UserAgreementPredicate> {

  private final UserAgreementRepository userAgreementRepository;

  @Override
  public Class<UserAgreement> getEntityClass() {
    return UserAgreement.class;
  }

  public List<UserAgreementDTO> findEnabledAgreementByUserId(Long userId) {
    return userAgreementRepository.findByUserId(userId);
  }

  public List<UserAgreement> findByUserId(Long userId) {
    return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<UserAgreement, List<UserAgreement>>() {
      @Override
      public List<UserAgreement> query() {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        return getResultList(entityManager, criteriaQuery, root, predicates, criteriaBuilder.asc(root.get(UserAgreement_.id)));
      }
    });
  }

  public UserAgreement findByCondition(Long userId, UserAgreementType userAgreementType) {
    return customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<UserAgreement, UserAgreement>() {
      @Override
      public UserAgreement query() {
        List<Predicate> predicates = new ArrayList<>();
        if(userId != null) {
          predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));          
        }
        if (userAgreementType != null) {
          predicates.add(predicate.equalUserAgreementType(criteriaBuilder, root, userAgreementType));          
        }
        return getSingleResult(entityManager, criteriaQuery, root, predicates, criteriaBuilder.asc(root.get(UserAgreement_.id)));
      }
    });
  }

}

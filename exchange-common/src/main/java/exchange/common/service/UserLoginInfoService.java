package exchange.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.stereotype.Service;

import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.UserLoginInfo;
import exchange.common.entity.UserLoginInfo_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.UserLoginInfoPredicate;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;

@Service
public class UserLoginInfoService extends EntityService<UserLoginInfo, UserLoginInfoPredicate> {

  @Override
  public Class<UserLoginInfo> getEntityClass() {
    return UserLoginInfo.class;
  }

  public List<UserLoginInfo> findByCondition(Long userId, int number, int size) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<UserLoginInfo, List<UserLoginInfo>>() {
          @Override
          public List<UserLoginInfo> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                number,
                size,
                criteriaBuilder.desc(root.get(UserLoginInfo_.id)));
          }
        });
  }

  public List<UserLoginInfo> findByCondition(Date from, Date to) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<UserLoginInfo, List<UserLoginInfo>>() {
          @Override
          public List<UserLoginInfo> query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, from));
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, to));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                ViewVariables.DEFAULT_NUMBER,
                ViewVariables.DEFAULT_SIZE,
                criteriaBuilder.asc(root.get(UserLoginInfo_.createdAt)));
          }
        });
  }

  public UserLoginInfo findOne(Long userId) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<UserLoginInfo, UserLoginInfo>() {
          @Override
          public UserLoginInfo query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(UserLoginInfo_.ID)));
          }
        });
  }
  
  private List<Predicate> getPredicatesOfFindByCondition(
	      CriteriaBuilder criteriaBuilder,
	      Root<UserLoginInfo> root,
	      Long userId,
	      String ipAddress,
	      Long dateFrom,
	      Long dateTo,
	      Boolean enabled,
	      Integer number,
	      Integer size) {
	    List<Predicate> predicates = new ArrayList<>();

	    if (userId != null) {
	      predicates.add(
	          predicate.equalUserId(criteriaBuilder, root, userId));
	    }
	    if (!StringUtils.isEmpty(ipAddress)) {
		      predicates.add(
		          predicate.equalIpAddress(criteriaBuilder, root, ipAddress));
		    }
	    if (dateFrom != null) {
	        predicates.add(
	        predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
	        }
	    if (dateTo != null) {
	        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
	        }
	    return predicates;
	  }
  
  public PageData<UserLoginInfo> findByConditionPageData(
	      Long userId, String ipAddress, Long dateFrom, Long dateTo, Integer number, Integer size) {
	    long count =
	        customTransactionManager.count(
	            getEntityClass(),
	            new QueryExecutorCounter<>() {
	              @Override
	              public Long query() {
	                return count(
	                    entityManager,
	                    criteriaBuilder,
	                    criteriaQuery,
	                    root,
	                    getPredicatesOfFindByCondition(
	                        criteriaBuilder, root, userId, ipAddress, dateFrom, dateTo, true, number, size));
	              }
	            });

	    return new PageData<UserLoginInfo>(
	        number,
	        size,
	        count,
	        customTransactionManager.find(
	            getEntityClass(),
	            new QueryExecutorReturner<UserLoginInfo, List<UserLoginInfo>>() {
	              @Override
	              public List<UserLoginInfo> query() {
	                List<Predicate> predicates =
	                    getPredicatesOfFindByCondition(
	                        criteriaBuilder, root, userId, ipAddress, dateFrom, dateTo, true, number, size);
	                return getResultList(
	                    entityManager,
	                    criteriaQuery,
	                    root,
	                    predicates,
	                    number,
	                    size,
	                    criteriaBuilder.desc(root.get(UserLoginInfo_.id)));
	              }
	            }));
	  }
  
  public List<UserLoginInfo> findByAllCondition(Long userId, String ipAddress, Long dateFrom, Long dateTo) {
	  return customTransactionManager.find(
		        getEntityClass(),
		        new QueryExecutorReturner<UserLoginInfo, List<UserLoginInfo>>() {
		          @Override
		          public List<UserLoginInfo> query() {
		            List<Predicate> predicates = 
		            		getPredicatesOfFindByCondition(
			                        criteriaBuilder, root, userId, ipAddress, dateFrom, dateTo, null, null, null);
		            return getResultList(
		            		entityManager,
		                    criteriaQuery,
		                    root,
		                    predicates,
		                    criteriaBuilder.asc(root.get(UserLoginInfo_.id)));
		          }
		        });
	}
}

package exchange.common.dal.master.mapper.user;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import exchange.common.dal.master.entity.user.UserInfoDO;
import exchange.common.mybatis.BatchMapper;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/12/10
 */
@Repository
public interface UserInfoMapper extends BatchMapper<UserInfoDO> {

	default void updateAntisocialStatusById(Long id, String antisocialStatus) {
		this.update(null, new LambdaUpdateWrapper<UserInfoDO>()
				.eq(UserInfoDO::getId, id)
				.set(UserInfoDO::getAntisocialStatus, antisocialStatus)
				.set(UserInfoDO::getUpdatedAt, new Date())
		);
	}

}

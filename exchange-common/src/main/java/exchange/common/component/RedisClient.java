package exchange.common.component;

import exchange.common.constant.ErrorCode;
import exchange.common.exception.CustomException;
import exchange.common.util.CollectionUtil;
import exchange.common.util.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.IntegerCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: wen.y
 * @date: 2024/10/9
 */
@Slf4j
@Component
public class RedisClient {

	@Autowired
	private RedissonClient redissonClient;

	private static final String REDIS_LOCK_PREFIX = "exchange:lock:";

	// worker, order用はredis lockがexpireされない設定とする(lockExpireSeconds=0)
	@Getter
	@AllArgsConstructor
	public enum LockParams {
		DEFAULT(5, 50, 100),
		ORDER(0, 3, 50),
		EXECUTE_ORDER(0, 1, 0),
		WORKER(0, 1, 0),
		LOCKKEY(0, 10, 100);

		private final int lockExpireSeconds;
		private final int gettingLockCount;
		private final long gettingLockIntervalMillis;
	}

	private RLock getLock(String keySuffix) {
		return redissonClient.getLock(REDIS_LOCK_PREFIX.concat(keySuffix));
	}

	public boolean tryLock(String keySuffix) {
		return getLock(keySuffix).tryLock();
	}

	public boolean tryLock(String keySuffix, long time, TimeUnit unit) {
		try {
			return getLock(keySuffix).tryLock(time, unit);
		} catch (InterruptedException e) {
			log.info("tryLock interrupted. keySuffix:{},time:{},unit:{}", keySuffix, time, unit);
			return false;
		}
	}

	public boolean tryLock(String keySuffix, LockParams lockParams) throws CustomException {
		RLock lock = getLock(keySuffix);
		try {
			for (int i = 0; i < lockParams.gettingLockCount; i++) {
				if ((lockParams.lockExpireSeconds > 0)) {
					if (lock.tryLock(lockParams.lockExpireSeconds, TimeUnit.SECONDS)) {
						return true;
					}
				} else {
					if (lock.tryLock()) {
						return true;
					}
				}

				if (lockParams.gettingLockIntervalMillis > 0) {
					Thread.sleep(lockParams.gettingLockIntervalMillis);
				}
			}
			return false;
		} catch (Exception e) {
			log.info("tryLock failed. keySuffix:{} lockParams:{}", keySuffix, JsonUtil.encode(lockParams));
			throw new CustomException(ErrorCode.COMMON_ERROR_LOCK, e);
		}
	}

	public void unlock(String keySuffix) {
		if (StringUtils.isBlank(keySuffix)) {
			return;
		}
		RLock lock = getLock(keySuffix);
		if (lock.isHeldByCurrentThread() && lock.isLocked()) {
			lock.unlock();
		}
	}

	private Duration convertToDuration(long time, TimeUnit unit) {
		return switch (unit) {
			case DAYS -> Duration.ofDays(time);
			case HOURS -> Duration.ofHours(time);
			case MINUTES -> Duration.ofMinutes(time);
			case SECONDS -> Duration.ofSeconds(time);
			case MILLISECONDS -> Duration.ofMillis(time);
			default -> throw new IllegalArgumentException("Unsupported time unit: " + unit);
		};
	}


	public String getString(String key) {
		RBucket<String> rBucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
		return rBucket.get();
	}

	public void setString(String key, String value) {
		RBucket<String> rBucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
		rBucket.set(value);
	}

	public void set(String key, String value, long timeToLive, TimeUnit timeUnit) {
		RBucket<String> rBucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
		rBucket.set(value, convertToDuration(timeToLive, timeUnit));
	}

	public Integer getInteger(String key) {
		RBucket<Integer> rBucket = redissonClient.getBucket(key, IntegerCodec.INSTANCE);
		return rBucket.get();
	}

	public void setInteger(String key, Integer value) {
		RBucket<Integer> rBucket = redissonClient.getBucket(key, IntegerCodec.INSTANCE);
		rBucket.set(value);
	}

	public void setInteger(String key,  Integer value, long timeToLive, TimeUnit timeUnit) {
		RBucket<Integer> rBucket = redissonClient.getBucket(key, IntegerCodec.INSTANCE);
		rBucket.set(value, convertToDuration(timeToLive, timeUnit));
	}

	public <T> T getObject(String key, Class<T> clazz) {
		String str = getString(key);
		if (StringUtils.isBlank(str)) {
			return null;
		}
		return JsonUtil.decode(str, clazz);
	}

	public void setObject(String key, Object value) {
		String val = JsonUtil.encode(value);
		RBucket<String> rBucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
		rBucket.set(val);
	}

	public void setObject(String key, Object value, long timeToLive, TimeUnit timeUnit) {
		String val = JsonUtil.encode(value);
		RBucket<String> rBucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
		rBucket.set(val, convertToDuration(timeToLive, timeUnit));
	}

	public void delete(String key) {
		redissonClient.getBucket(key).delete();
	}

	public void delete(List<String> keys) {
		if (CollectionUtil.isEmpty(keys)) {
		  return;
		}
		keys.forEach(this::delete);
	}

	public boolean executeWithLock(String keySuffix, LockParams lockParams, Executor executor) throws Exception {
		try {
			if (!tryLock(keySuffix, lockParams)) {
				log.info("executeWithLock get lock failed. keySuffix: {}",  keySuffix);
				return false;
			}
			executor.execute();
			return true;
		} catch (Exception e) {
			log.error("executeWithLock error", e);
			throw new CustomException(e);
		} finally {
			unlock(keySuffix);
		}
	}


	public Object executeWithLock(String keySuffix, LockParams lockParams, ExecutorReturner<?> executorReturner) throws Exception {
		try {
			if (!tryLock(keySuffix, lockParams)) {
				log.info("executeWithLock get lock failed. keySuffix: {}",  keySuffix);
				return false;
			}
			return executorReturner.execute();
		} catch (Exception e) {
			log.error("executeWithLock error", e);
			throw new CustomException(e);
		} finally {
			unlock(keySuffix);
		}
	}

	public Object executeWithLock(String keySuffix, ExecutorReturner<?> executorReturner) throws Exception {
		try {
			if (!tryLock(keySuffix)) {
				log.info("executeWithLock get lock failed. keySuffix: {}",  keySuffix);
				return false;
			}
			return executorReturner.execute();
		} catch (Exception e) {
			log.error("executeWithLock error", e);
			throw new CustomException(e);
		} finally {
			unlock(keySuffix);
		}
	}
}

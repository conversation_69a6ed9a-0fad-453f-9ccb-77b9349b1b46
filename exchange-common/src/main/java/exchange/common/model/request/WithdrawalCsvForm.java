package exchange.common.model.request;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;

public class WithdrawalCsvForm {

	  @Getter
	  @Setter
	  @NotNull
	  private Long userId;

	  @Getter
	  @Setter
	  @NotNull
	  private String currency;

	  @Getter
	  @Setter
	  @NotNull
	  private BigDecimal amount;

	  @Getter
	  @Setter
	  @NotNull
	  private BigDecimal withdrawalFee;

	  @Getter
	  @Setter
	  @NotNull
	  private BigDecimal transactionFee;

	  @Getter
	  @Setter
	  private String comment;

	  @Getter
	  @Setter
	  @NotNull
	  private Long withdrawalAccountId;

	  @Getter
	  @Setter
	  @NotNull
	  private String address;

	  @Getter
	  @Setter
	  private String transactionId;

	  @Getter
	  @Setter
	  @NotNull
	  private String withdrawalStatus;

	  @Getter
	  @Setter
	  @NotNull
	  private String createdAt;
}

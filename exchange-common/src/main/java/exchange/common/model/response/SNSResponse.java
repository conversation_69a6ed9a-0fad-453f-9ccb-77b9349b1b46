package exchange.common.model.response;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SNSResponse implements Serializable {

  private String code;

  private String message;

  private String delivery_id;

  private String accepted_at;

  private String reserved_at;

  private List<contact> contacts;

  private List<String> click_count_urls;

  @Getter
  @Setter
  @NoArgsConstructor
  @AllArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class contact{
    private String contact_id;

    private String phone_number;

    private String result_code;

    private String result_message;
  }

}

package exchange.common.constant;

import com.baomidou.mybatisplus.annotation.IEnum;

public enum UserStatus implements IEnum<String> {
  REVIEWING, // 審査中
  ACTIVE, // 有効
  BANNED, // 強制停止
  UNTRADABLE, // 取引停止
  UNWITHDRAWABLE, // 出金停止
  LEFT; // 退会済み

  public static UserStatus valueOfName(String name) {
    for (UserStatus userStatus : values()) {
      if (userStatus.name().equals(name)) {
        return userStatus;
      }
    }

    return null;
  }

  @Override
  public String getValue() {
    return this.name();
  }
}

package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import exchange.common.constant.Currency;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(DepositAccount.class)
public abstract class DepositAccount_ extends AbstractEntity_ {

  public static volatile SingularAttribute<DepositAccount, String> address;
  public static volatile SingularAttribute<DepositAccount, Long> destinationTag;
  public static volatile SingularAttribute<DepositAccount, Currency> currency;
  public static volatile SingularAttribute<DepositAccount, Long> userId;
  public static volatile SingularAttribute<DepositAccount, User> user;
  public static volatile SingularAttribute<DepositAccount, Boolean> enabled;

  public static final String ADDRESS = "address";
  public static final String DESTINATION_TAG = "destinationTag";
  public static final String CURRENCY = "currency";
  public static final String USER_ID = "userId";
  public static final String USER = "user";
  public static final String ENABLED = "enabled";

}


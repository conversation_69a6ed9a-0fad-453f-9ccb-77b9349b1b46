package exchange.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PosControl.class)
public abstract class PosControl_ extends AbstractEntity_ {

  public static volatile SingularAttribute<StakingApplyDetail, Boolean> displayFlg;
  

}

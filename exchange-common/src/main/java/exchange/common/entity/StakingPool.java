package exchange.common.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import exchange.common.constant.Currency;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "staking_pool")
@ToString(callSuper = true, doNotUseGetters = true)
public class StakingPool extends AbstractEntity {

  private static final long serialVersionUID = 3643980084099700186L;

  @Getter
  @Setter
  @Column(name = "currency", nullable = false)
  @Enumerated(EnumType.STRING)
  private Currency currency;

  @Getter
  @Setter
  @Column(name = "amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal amount;

}

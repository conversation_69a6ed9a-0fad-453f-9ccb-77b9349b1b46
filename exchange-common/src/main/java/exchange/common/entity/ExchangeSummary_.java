package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ExchangeSummary.class)
public abstract class ExchangeSummary_ extends AbstractEntity_ {

  public static volatile SingularAttribute<ExchangeSummary, Date> targetAt;
  public static volatile SingularAttribute<ExchangeSummary, Currency> currency;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> currentAmount;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> jpyConversion;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> depositAmount;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> depositAmountJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> depositFee;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> depositFeeJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> withdrawalAmount;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> withdrawalAmountJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> withdrawalFee;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> withdrawalFeeJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> transactionFee;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> transactionFeeJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> exchangeSpotTradeBuyAmount;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
      exchangeSpotTradeBuyAmountJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> exchangeSpotTradeSellAmount;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
      exchangeSpotTradeSellAmountJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> exchangeSpotTradeFee;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> exchangeSpotTradeFeeJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
      simpleMarketSpotTradeBuyAmount;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
      simpleMarketSpotTradeBuyAmountJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
      simpleMarketSpotTradeSellAmount;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
      simpleMarketSpotTradeSellAmountJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> simpleMarketSpotTradeFee;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> simpleMarketSpotTradeFeeJpy;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal> simpleMarketSpotTradeProfit;
  public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
      simpleMarketSpotTradeProfitJpy;

  public static final String TARGET_AT = "targetAt";
  public static final String CURRENCY = "currency";
  public static final String CURRENT_AMOUNT = "currentAmount";
  public static final String JPY_CONVERSION = "jpyConversion";
  public static final String DEPOSIT_AMOUNT = "depositAmount";
  public static final String DEPOSIT_AMOUNT_JPY = "depositAmountJpy";
  public static final String DEPOSIT_FEE = "depositFee";
  public static final String DEPOSIT_FEE_JPY = "depositFeeJpy";
  public static final String WITHDRAWAL_AMOUNT = "withdrawalAmount";
  public static final String WITHDRAWAL_AMOUNT_JPY = "withdrawalAmountJpy";
  public static final String WITHDRAWAL_FEE = "withdrawalFee";
  public static final String WITHDRAWAL_FEE_JPY = "withdrawalFeeJpy";
  public static final String TRANSACTION_FEE = "transactionFee";
  public static final String TRANSACTION_FEE_JPY = "transactionFeeJpy";
  public static final String EXCHANGE_SPOT_TRADE_BUY_AMOUNT = "exchangeSpotTradeBuyAmount";
  public static final String EXCHANGE_SPOT_TRADE_BUY_AMOUNT_JPY = "exchangeSpotTradeBuyAmountJpy";
  public static final String EXCHANGE_SPOT_TRADE_SELL_AMOUNT = "exchangeSpotTradeSellAmount";
  public static final String EXCHANGE_SPOT_TRADE_SELL_AMOUNT_JPY = "exchangeSpotTradeSellAmountJpy";
  public static final String EXCHANGE_SPOT_TRADE_FEE = "exchangeSpotTradeFee";
  public static final String EXCHANGE_SPOT_TRADE_FEE_JPY = "exchangeSpotTradeFeeJpy";
  public static final String SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT = "simpleMarketPosTradeBuyAmount";
  public static final String SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT_JPY =
      "simpleMarketPosTradeBuyAmountJpy";
  public static final String SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT =
      "simpleMarketPosTradeSellAmount";
  public static final String SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT_JPY =
      "simpleMarketPosTradeSellAmountJpy";
  public static final String SIMPLE_MARKET_POS_TRADE_FEE = "simpleMarketPosTradeFee";
  public static final String SIMPLE_MARKET_POS_TRADE_FEE_JPY = "simpleMarketPosTradeFeeJpy";
  public static final String SIMPLE_MARKET_SPOT_TRADE_PROFIT = "simpleMarketSpotTradeProfit";
  public static final String SIMPLE_MARKET_SPOT_TRADE_PROFIT_JPY = "simpleMarketSpotTradeProfitJpy";
}

package exchange.common.entity.dowjones;

import exchange.common.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serial;

/**
 * @author: wen.y
 * @date: 2024/12/12
 */
@Entity
@NoArgsConstructor
@Table(name = "dow_jones_sam_association")
@ToString(callSuper = true, doNotUseGetters = true)
public class DowJonesSamAssociation extends AbstractEntity {

	@Serial
	private static final long serialVersionUID = -6098572054869994238L;

	@Getter
	@Setter
	@Column(name = "case_id")
	private Long caseId;

	@Getter
	@Setter
	@Column(name = "dow_jones_sam_association_id")
	private String dowJonesSamAssociationId;

	@Getter
	@Setter
	@Column(name = "user_relation_data_type")
	private String userRelationDataType;

	@Getter
	@Setter
	@Column(name = "user_relation_data_id")
	private Long userRelationDataId;

	@Getter
	@Setter
	@Column(name = "user_relation_data_info")
	private String userRelationDataInfo;

	@Getter
	@Setter
	@Column(name = "enabled")
	private Boolean enabled;
}

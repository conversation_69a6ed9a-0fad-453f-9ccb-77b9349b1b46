package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.stereotype.Component;

import exchange.common.constant.Currency;
import exchange.common.entity.DepositAccount;
import exchange.common.entity.DepositAccount_;

@Component
public class DepositAccountPredicate extends EntityPredicate<DepositAccount> {

  public Predicate equalUserId(
      CriteriaBuilder criteriaBuilder, Root<DepositAccount> root, Long userId) {
    return criteriaBuilder.equal(root.get(DepositAccount_.userId), userId);
  }

  public Predicate equalCurrency(
      CriteriaBuilder criteriaBuilder, Root<DepositAccount> root, Currency currency) {
    return criteriaBuilder.equal(root.get(DepositAccount_.currency), currency);
  }

  public Predicate equalAddress(
      CriteriaBuilder criteriaBuilder, Root<DepositAccount> root, String address) {
    return criteriaBuilder.equal(root.get(DepositAccount_.address), address);
  }

  public Predicate likeAddress(
      CriteriaBuilder criteriaBuilder, Root<DepositAccount> root, String address) {
    return criteriaBuilder.or(criteriaBuilder.like(root.get(DepositAccount_.address), address + "@" + "%"), 
        criteriaBuilder.equal(root.get(DepositAccount_.address), address));
  }
  
  public Predicate likeDestinationTag(
      CriteriaBuilder criteriaBuilder, Root<DepositAccount> root, String destinationTag) {
    return criteriaBuilder.like(root.get(DepositAccount_.address), "%" + "@" + destinationTag);
  }

  public Predicate equalDestinationTag(
      CriteriaBuilder criteriaBuilder, Root<DepositAccount> root, Long destinationTag) {
    return criteriaBuilder.equal(root.get(DepositAccount_.destinationTag), destinationTag);
  }

  public Predicate isEnabled(
      CriteriaBuilder criteriaBuilder, Root<DepositAccount> root, boolean enabled) {
    return enabled
        ? criteriaBuilder.isTrue(root.get(DepositAccount_.enabled))
        : criteriaBuilder.isFalse(root.get(DepositAccount_.enabled));
  }
}

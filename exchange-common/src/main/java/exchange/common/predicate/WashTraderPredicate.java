package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.entity.WashTrader;
import exchange.common.entity.WashTrader_;

@Component
public class WashTraderPredicate extends EntityPredicate<WashTrader> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<WashTrader> root,
      Long userId) {
    return criteriaBuilder.equal(root.get(WashTrader_.userId), userId);
  }

}

package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import exchange.common.entity.Order;
import exchange.common.entity.Order_;

public abstract class OrderPredicate<E extends Order> extends EntityPredicate<E> {

  public Predicate equalSymbolId(CriteriaBuilder criteriaBuilder, Root<E> root, Long symbolId) {
    return criteriaBuilder.equal(root.get(Order_.symbolId), symbolId);
  }

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<E> root, Long userId) {
    return criteriaBuilder.equal(root.get(Order_.userId), userId);
  }
}

package exchange.common.util;

import java.math.BigInteger;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.UUID;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;

public class SignatureUtil {

  public enum Algorithm {
    HMAC_SHA256("HmacSHA256"),
    HMAC_SHA512("HmacSHA512"),
    MD5("MD5"),
    SHA1("SHA-1"),
    SHA256("SHA-256"),
    SHA512("SHA-512");

    private final String code;

    Algorithm(String code) {
      this.code = code;
    }
  }

  public static byte[] doFinal(Algorithm algorithm, byte[] secret, byte[] input) {
    Mac mac;

    try {
      mac = Mac.getInstance(algorithm.code);
    } catch (NoSuchAlgorithmException e) {
      e.printStackTrace();
      return null;
    }

    try {
      mac.init(new SecretKeySpec(secret, algorithm.code));
    } catch (InvalidKeyException e) {
      e.printStackTrace();
      return null;
    }

    return mac.doFinal(input);
  }

  public static byte[] doFinal(Algorithm algorithm, String secret, byte[] input) {
    return doFinal(algorithm, secret.getBytes(), input);
  }

  public static String getBase64Hash(Algorithm algorithm, String secret, String input) {
    return Base64.encodeBase64String(doFinal(algorithm, secret, input.getBytes()));
  }

  public static String getHash(Algorithm algorithm, String secret, String input) {
    return String.format("%064x", new BigInteger(1, doFinal(algorithm, secret, input.getBytes())));
  }

  public static byte[] digest(Algorithm algorithm, byte[] input) {
    MessageDigest messageDigest;

    try {
      messageDigest = MessageDigest.getInstance(algorithm.code);
    } catch (NoSuchAlgorithmException e) {
      e.printStackTrace();
      return null;
    }

    return messageDigest.digest(input);
  }

  public static byte[] digest(Algorithm algorithm, String input) {
    return digest(algorithm, input.getBytes());
  }

  public static String getHash(Algorithm algorithm, String input) {
    return String.format("%064x", new BigInteger(1, digest(algorithm, input)));
  }

  public static String getBitbankSignature(String secret, String nonce, String seed) {
    return getHash(Algorithm.HMAC_SHA256, secret, nonce + seed);
  }

  public static String getUuidV3(String input) {
    return UUID.nameUUIDFromBytes(input.getBytes()).toString();
  }

  public static String stringify(List<NameValuePair> nameValuePairs) {
    return URLEncodedUtils.format(nameValuePairs, CharsetUtil.UTF_8.getCharset());
  }
}

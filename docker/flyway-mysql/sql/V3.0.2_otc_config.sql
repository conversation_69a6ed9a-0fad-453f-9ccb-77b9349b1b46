
CREATE TABLE `otc_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `currency` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通貨',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'アドレス',
  `enabled` bit(1) NOT NULL COMMENT '有効',
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
  `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;

ALTER TABLE withdrawal ADD distinction varchar(128) NOT NULL DEFAULT 'OUT' AFTER `transaction_id` ;

ALTER TABLE deposit ADD distinction varchar(128) NOT NULL DEFAULT 'IN' AFTER `transaction_id` ;

package exchange.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.common.constant.CandlestickType;
import exchange.common.constant.TradeType;
import exchange.common.entity.Candlestick;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.service.CandlestickService;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.SymbolService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/candlestick")
@Timed
public class V1CandlestickRestController {

  private final SymbolService symbolService;
  private final CurrencyPairConfigService currencyPairConfigService;

  @GetMapping
  public ResponseEntity<CandlestickApiResponse> get(
      HttpServletResponse response,
      @RequestParam(value = "symbolId") Long symbolId,
      @RequestParam(value = "candlestickType") String paramCandlestickType,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo)
      throws Exception {
    List<Candlestick> candlesticks = new ArrayList<>();
    Symbol symbol = symbolService.findOne(symbolId);

    if (symbol == null) {
      return ResponseEntity.ok(CandlestickApiResponse.createEmpty());
    }

    // 入力のsymbolIdが有効な(enabled=true)通貨ペアかチェック
    List<CurrencyPairConfig> currencyPairConfigs =
        currencyPairConfigService.findAllByCondition(
            TradeType.SPOT, symbol.getCurrencyPair(), true);

    if (CollectionUtils.isEmpty(currencyPairConfigs)) {
      return ResponseEntity.ok(CandlestickApiResponse.create(symbol, candlesticks));
    }

    CandlestickType candlestickType = CandlestickType.valueOf(paramCandlestickType);

    response.setHeader("Cache-Control", "public, max-age=1");

    // 最新の足(降順)から500件固定で取得し、昇順に戻す
    candlesticks =
        CandlestickService.getBean(symbol)
            .findByCondition(
                symbolId,
                candlestickType,
                dateFrom != null ? new Date(dateFrom) : null,
                dateTo != null ? new Date(dateTo) : null,
                500);

    Collections.reverse(candlesticks);

    return ResponseEntity.ok(CandlestickApiResponse.create(symbol, candlesticks));
  }

  record CandlestickElement(
      BigDecimal open, // 始値
      BigDecimal high, // 高値
      BigDecimal low, // 安値
      BigDecimal close, // 終値
      BigDecimal volume, // 出来高
      Long time // 対象日時
  ){
    static CandlestickElement create(Symbol symbol, Candlestick candlestick) {
      final var currencyPair = symbol.getCurrencyPair();
      return new CandlestickElement(
          currencyPair.getScaledPrice(candlestick.getOpen()), // 始値
          currencyPair.getScaledPrice( candlestick.getHigh()), // 高値
          currencyPair.getScaledPrice(candlestick.getLow()), // 安値
          currencyPair.getScaledPrice(candlestick.getClose()), // 終値
          currencyPair.getScaledAmount(candlestick.getVolume()), // 出来高
          new Date().getTime() // 対象日時
      );
    }
  }

  record CandlestickApiResponse(
    Long symbolId,
    List<CandlestickElement> candlesticks,
    Long timestamp
  ) {
    static CandlestickApiResponse create(Symbol symbol, List<Candlestick> candlesticks) {
      final var candlestickElements = candlesticks
          .stream()
          .map(it -> CandlestickElement.create(symbol, it))
          .toList();
      return new CandlestickApiResponse(
          symbol.getId(),
          candlestickElements,
          new Date().getTime()
      );
    }

    static CandlestickApiResponse createEmpty() {
      return new CandlestickApiResponse(
          null,
          new ArrayList<>(),
          new Date().getTime()
      );
    }
  }
}

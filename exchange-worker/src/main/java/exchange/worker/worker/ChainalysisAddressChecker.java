package exchange.worker.worker;

import exchange.common.aml.WorldCheckOneRes;
import exchange.common.component.ChainalysisManager;
import exchange.common.component.CustomTransactionManager;
import exchange.common.component.RedisManager;
import exchange.common.component.SesManager;
import exchange.common.config.ChainalysisConfig;
import exchange.common.constant.*;
import exchange.common.entity.*;
import exchange.common.exception.CustomException;
import exchange.common.model.response.chainalysis.ChainalysisWithdrawalAttemptsAlertsResponse;
import exchange.common.model.response.chainalysis.ChainalysisWithdrawalAttemptsRegisterResponse;
import exchange.common.model.response.chainalysis.ChainalysisWithdrawalAttemptsSummaryResponse;
import exchange.common.repos.DepositRepository;
import exchange.common.repos.WithdrawalRepository;
import exchange.common.service.*;
import exchange.common.service.dowjones.DowJonesAntisocialCheckService;
import exchange.common.util.CollectionUtil;
import exchange.common.util.FormatUtil;
import exchange.common.util.JsonUtil;
import exchange.worker.component.Worker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wen.y
 * @date: 2024/8/12
 */
@Slf4j
@Component
public class ChainalysisAddressChecker extends Worker {
	@Resource
	private ChainalysisConfig chainalysisConfig;
	@Resource
	private ChainalysisManager chainalysisManager;
	@Resource
	private WithdrawalService withdrawalService;
	@Resource
	private DepositService depositService;
	@Resource
	private WithdrawalRepository withdrawalRepository;
	@Resource
	private DepositRepository depositRepository;
	@Resource
	private RedisManager redisManager;
	@Resource
	private CustomTransactionManager customTransactionManager;
	@Resource
	private AssetService assetService;
	@Resource
	private MailNoreplyService mailNoreplyService;
	@Resource
	private SesManager sesManager;
	@Resource
	private UserService userService;

	@Resource
	private DowJonesAntisocialCheckService dowJonesAntisocialCheckService;

	@Override
	public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
		long startTime = System.currentTimeMillis();
		log.info("ChainalysisAddressChecker start. 暗号資産アドレス分析開始");
		try {
			// 出金アドレス審査
			List<Withdrawal> withdrawalList = new ArrayList<>(
				withdrawalService.findByCondition(
					null,
					null,
					null,
					null,
					null,
					null,
					WithdrawalStatus.SYGNA_COOPERATION_UNABLE,
					null,
					0,
					Integer.MAX_VALUE));
			log.info("ChainalysisAddressChecker wait analysis withdrawal address count:{}", withdrawalList.size());
			if (CollectionUtil.isNotEmpty(withdrawalList)) {
				for (Withdrawal withdrawal : withdrawalList) {
					if (null == withdrawal.getWithdrawalAccountId() || null == withdrawal.getWithdrawalAccount()) {
						log.warn("ChainalysisAddressChecker withdrawal account is null skip id:{},userId:{},currency:{},address:{}",
								withdrawal.getId(), withdrawal.getUserId(), withdrawal.getCurrency(), withdrawal.getAddress());
						continue;
					}
					ChainalysisAlertLevel alertLevel = withdrawalAnalyses(withdrawal);
					withdrawalAnalysesResultProcess(withdrawal.getId(), alertLevel);
				}
			}

			// 入金アドレス審査
			List<Deposit> depositList = new ArrayList<>(
				depositService.findByCondition(
					null,
					null,
					null,
					null,
					null,
					null,
					DepositStatus.SENDER_INFO_RECIVED,
					null,
					0,
					Integer.MAX_VALUE));
			log.info("ChainalysisAddressChecker wait analysis deposit address count:{}", depositList.size());
			if (CollectionUtil.isNotEmpty(depositList)) {
				for (Deposit deposit : depositList) {
					ChainalysisAlertLevel alertLevel = depositAnalyses(deposit);
					depositAnalysesResultProcess(deposit.getId(), alertLevel);
				}
			}
		} finally{
			long endTime = System.currentTimeMillis();
			long seconds = (endTime - startTime) / 1000;
			log.info("ChainalysisAddressChecker end. 暗号資産アドレス分析終了. 実行時間: {} minute {} seconds",  seconds / 60, seconds % 60);
		}
	}

	private ChainalysisAlertLevel withdrawalAnalyses(Withdrawal withdrawal) {
		try{
			if (null != withdrawal.getChainalysisMaxAlertLevel()) {
				return withdrawal.getChainalysisMaxAlertLevel();
			}

			if (StringUtils.isBlank(withdrawal.getChainalysisExternalId())) {
				// execute analyses
				ChainalysisWithdrawalAttemptsRegisterResponse attemptsRegisterResponse = chainalysisManager.withdrawalAttemptsRegister(withdrawal.getUserId(), withdrawal.getCurrency(), withdrawal.getAddress());
				if (null != attemptsRegisterResponse && StringUtils.isNotBlank(attemptsRegisterResponse.getExternalId())) {
					withdrawal.setChainalysisExternalId(attemptsRegisterResponse.getExternalId());
					withdrawalRepository.updateByIdAndChainalysisExternalId(withdrawal.getId(), withdrawal.getChainalysisExternalId(), new Date());
				} else {
					log.error("ChainalysisAddressChecker withdrawal process api attempts register response error id:{},userId:{},currency:{},address:{},response:{}",
							withdrawal.getId(), withdrawal.getUserId(), withdrawal.getCurrency(), withdrawal.getAddress(), JsonUtil.encode(attemptsRegisterResponse));
				}
			}

			ChainalysisWithdrawalAttemptsSummaryResponse attemptsSummaryResponse = chainalysisManager.withdrawalAttemptsSummary(withdrawal.getChainalysisExternalId());
			if (null != attemptsSummaryResponse) {
				// check analyses completed
				if (attemptsSummaryResponse.isCompleted()) {
					ChainalysisWithdrawalAttemptsAlertsResponse attemptsAlertsResponse = chainalysisManager.withdrawalAttemptsAlerts(withdrawal.getChainalysisExternalId());
					if (null != attemptsAlertsResponse) {
						return attemptsAlertsResponse.getMaxAlertLevel();
					} else {
						log.error("ChainalysisAddressChecker withdrawal process api attempts alerts response error id:{},userId:{},currency:{},address:{},response:{}",
								withdrawal.getId(), withdrawal.getUserId(), withdrawal.getCurrency(), withdrawal.getAddress(), JsonUtil.encode(attemptsSummaryResponse));
					}
				}
			} else {
				log.error("ChainalysisAddressChecker withdrawal process api attempts summary response error id:{},userId:{},currency:{},address:{},response:{}",
						withdrawal.getId(), withdrawal.getUserId(), withdrawal.getCurrency(), withdrawal.getAddress(), JsonUtil.encode(attemptsSummaryResponse));
			}
		} catch (Exception e) {
			log.error("ChainalysisAddressChecker withdrawal process error id:{},userId:{},currency:{},address:{}",
					withdrawal.getId(), withdrawal.getUserId(), withdrawal.getCurrency(), withdrawal.getAddress());
		}
		return null;
	}



	private ChainalysisAlertLevel depositAnalyses(Deposit deposit) {
		try{
			if (null != deposit.getChainalysisMaxAlertLevel()) {
				return deposit.getChainalysisMaxAlertLevel();
			}

			if (StringUtils.isBlank(deposit.getChainalysisExternalId())) {
				// execute analyses
				ChainalysisWithdrawalAttemptsRegisterResponse attemptsRegisterResponse = chainalysisManager.withdrawalAttemptsRegister(deposit.getUserId(), deposit.getCurrency(), deposit.getAddress());
				if (null != attemptsRegisterResponse && StringUtils.isNotBlank(attemptsRegisterResponse.getExternalId())) {
					deposit.setChainalysisExternalId(attemptsRegisterResponse.getExternalId());
					depositRepository.updateByIdAndChainalysisExternalId(deposit.getId(), deposit.getChainalysisExternalId(), new Date());
				} else {
					log.error("ChainalysisAddressChecker deposit process api attempts register response error id:{},userId:{},currency:{},address:{},response:{}",
							deposit.getId(), deposit.getUserId(), deposit.getCurrency(), deposit.getAddress(), JsonUtil.encode(attemptsRegisterResponse));
				}
			}

			ChainalysisWithdrawalAttemptsSummaryResponse attemptsSummaryResponse = chainalysisManager.withdrawalAttemptsSummary(deposit.getChainalysisExternalId());
			if (null != attemptsSummaryResponse) {
				// check analyses completed
				if (attemptsSummaryResponse.isCompleted()) {
					ChainalysisWithdrawalAttemptsAlertsResponse attemptsAlertsResponse = chainalysisManager.withdrawalAttemptsAlerts(deposit.getChainalysisExternalId());
					if (null != attemptsAlertsResponse) {
						return attemptsAlertsResponse.getMaxAlertLevel();
					} else {
						log.error("ChainalysisAddressChecker deposit process api attempts alerts response error id:{},userId:{},currency:{},address:{},response:{}",
								deposit.getId(), deposit.getUserId(), deposit.getCurrency(), deposit.getAddress(), JsonUtil.encode(attemptsSummaryResponse));
					}
				}
			} else {
				log.error("ChainalysisAddressChecker deposit process api attempts summary response error id:{},userId:{},currency:{},address:{},response:{}",
						deposit.getId(), deposit.getUserId(), deposit.getCurrency(), deposit.getAddress(), JsonUtil.encode(attemptsSummaryResponse));
			}
		} catch (Exception e) {
			log.error("ChainalysisAddressChecker deposit process error id:{},userId:{},currency:{},address:{}",
					deposit.getId(), deposit.getUserId(), deposit.getCurrency(), deposit.getAddress());
		}
		return null;
	}

	private void withdrawalAnalysesResultProcess(Long id, ChainalysisAlertLevel alertLevel) throws Exception {
		if (null == alertLevel) {
			return;
		}

		Withdrawal withdrawalForUpdate = withdrawalService.findOne(id);
		Boolean hasRisk = dowJonesAntisocialCheckService.checkRiskByWithdrawalId(id);
		log.info("ChainalysisAddressChecker withdrawal id={},alertLevel={},hasDowJonesRisk={}", id, alertLevel, hasRisk);
		if (alertLevel.getRiskScore().compareTo(chainalysisConfig.getAlertLevelLimit().getRiskScore()) < 0
			&& !hasRisk) {
			withdrawalForUpdate.setSanctionMatch(false);

			if("プライベートウォレット等".equals(withdrawalForUpdate.getWithdrawalAccount().getAddresstype())) {
				withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.AML_INFO_RECIVED);
			} else {
				withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.APPROVED);
			}
		} else {
			withdrawalForUpdate.setSanctionMatch(true);
			withdrawalForUpdate.setWithdrawalStatus(WithdrawalStatus.AML_INFO_RECIVED);
		}
		withdrawalForUpdate.setRiskScore(alertLevel.getRiskScore());
		withdrawalForUpdate.setChainalysisMaxAlertLevel(alertLevel);
		withdrawalService.save(withdrawalForUpdate);
	}

	private void depositAnalysesResultProcess(Long id, ChainalysisAlertLevel alertLevel) throws Exception {
		if (null == alertLevel) {
			return;
		}

		Deposit depositForUpdate = depositService.findOne(id);
		Boolean hasRisk = dowJonesAntisocialCheckService.checkRiskByDepositId(id);
		log.info("ChainalysisAddressChecker deposit id={},alertLevel={},hasDowJonesRisk={}", id, alertLevel, hasRisk);
		if (alertLevel.getRiskScore().compareTo(chainalysisConfig.getAlertLevelLimit().getRiskScore()) < 0
			&& !hasRisk) {
			if (!redisManager.executeWithLock(getLockKey(depositForUpdate.getUserId(), depositForUpdate.getCurrency()), RedisManager.LockParams.LOCKKEY, () -> {
				customTransactionManager.execute(
						entityManager -> {
							depositForUpdate.setDepositStatus(DepositStatus.DONE);
							depositForUpdate.setSanctionMatch(false);
							depositForUpdate.setRiskScore(alertLevel.getRiskScore());
							depositForUpdate.setChainalysisMaxAlertLevel(alertLevel);
							depositService.save(depositForUpdate);
							//　資産更新 ロック解除
							assetService.updateWithExternalLock(
									depositForUpdate.getUserId(),
									depositForUpdate.getCurrency(),
									BigDecimal.ZERO,
									depositForUpdate.getAmount().subtract(depositForUpdate.getFee()).negate(),
									entityManager);
							return depositForUpdate;
						});
			})) {
				log.error(getClass().getSimpleName() + ",could not get lock. key: " + getLockKey(depositForUpdate.getUserId(), depositForUpdate.getCurrency()));
				throw new CustomException(ErrorCode.LOCK_KEY, "AssetUpdate userId: " + depositForUpdate.getUserId());
			}
			if (DepositStatus.DONE.equals(depositForUpdate.getDepositStatus())) {
				// メール送信：入金承認
				MailNoreplyType mailNoreplyType = MailNoreplyType.DEPOSIT_RECOGNITION_CODE;
				MailNoreply mailNoreply = mailNoreplyService.findOne(mailNoreplyType);
				String date = FormatUtil.formatJst(depositForUpdate.getUpdatedAt(), FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
				String currency = depositForUpdate.getCurrency().getName();
				String amount = depositForUpdate.getAmount().toString();
				String addressFrom = depositForUpdate.getAddress();
				String txHash = depositForUpdate.getTransactionHash();
				MessageFormat messageFormat = new MessageFormat(mailNoreply.getContents());
				String[] args = new String[] {date, currency, amount, addressFrom, txHash};
				String mailContent = messageFormat.format(args);
				User user = userService.findOne(depositForUpdate.getUserId());
				sesManager.send(
						mailNoreply.getFromAddress(),
						user.getEmail(),
						mailNoreply.getTitle(),
						mailContent);
			}
		} else {
			customTransactionManager.execute(
					entityManager -> {
						depositForUpdate.setDepositStatus(DepositStatus.AML_INFO_RECIVED);
						depositForUpdate.setRiskScore(alertLevel.getRiskScore());
						depositForUpdate.setChainalysisMaxAlertLevel(alertLevel);
						depositForUpdate.setSanctionMatch(true);
						depositService.save(depositForUpdate);
					});
		}
	}

	private static String getLockKey(Long userId, Currency currency) {
		return "lock:asset:" + userId + ":" + currency;
	}
}

package exchange.worker.worker;

import java.text.MessageFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import exchange.common.component.SesManager;
import exchange.common.constant.BulktransferStatus;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.MailNoreplyType;
import exchange.common.entity.Asset;
import exchange.common.entity.FiatWithdrawal;
import exchange.common.entity.MailNoreply;
import exchange.common.entity.Symbol;
import exchange.common.exception.CustomException;
import exchange.common.model.response.BulkTransferDetail;
import exchange.common.model.response.BulkTransferInfo;
import exchange.common.model.response.BulkTransferStatusResponse;
import exchange.common.service.AssetService;
import exchange.common.service.FiatWithdrawalService;
import exchange.common.service.GmoService;
import exchange.common.service.MailNoreplyService;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.worker.component.Worker;
import io.netty.util.internal.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class BulkTransferResultChecker extends Worker {

  private final FiatWithdrawalService fiatWithdrawalService;
  private final MailNoreplyService mailNoreplyService;
  private final GmoService gmoService;
  private final AssetService assetService;
  private final SesManager sesManager;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    log.info(" ==============================BulkTransferResultChecker start==============================");
    log.info("総合振込依頼結果照会と状況照会は開始しました。");
    // 総合振込依頼結果照会と状況照会
    bulkTransferResultAndStatus();

    log.info(" ==============================BulkTransferResultChecker end==============================");
    log.info("総合振込依頼結果照会と状況照会は終了しました。");
  }

  /**
   * 総合振込依頼結果と状況照会
   * 
   * @throws Exception
   */
  private void bulkTransferResultAndStatus() throws Exception {
    List<String> applyNoList = new ArrayList<>();
    List<FiatWithdrawal> bulkTransferResultTargets =
        fiatWithdrawalService.findBulkTransferResultTarget();
    if (bulkTransferResultTargets == null || bulkTransferResultTargets.size() == 0) {
      return;
    }
    String compareApplyNo = "";
    for (FiatWithdrawal fiatWithdrawal : bulkTransferResultTargets) {
      if (!compareApplyNo.equals(fiatWithdrawal.getApplyNo())) {
        compareApplyNo = fiatWithdrawal.getApplyNo();
        applyNoList.add(fiatWithdrawal.getApplyNo());
      }

    }

    // 総合振込依頼結果照会
    int count = 0;
    for (String applyNo : applyNoList) {
      count++;
      String resultCode = gmoService.getBulktransferRequestResult(applyNo);

      if ("8".equals(resultCode)) {
        // 依頼が期限切の場合、何もしない、次回の振込依頼を待ち

      } else if ("1".equals(resultCode)) {
        // 依頼が完了の場合
        Long sleepTime = 1000L;
        Thread.sleep(sleepTime);
        log.info("sleep: " + sleepTime + "ms");
        
        // 総合振込状況照会
        BulkTransferStatusResponse bulkTransferStatusResponse = gmoService.getBulktransferStatus(applyNo);
        
        if (count < applyNoList.size()) {
          Thread.sleep(sleepTime);
          log.info("not last and sleep: " + sleepTime + "ms");
        }
        // 総合振込照会明細情報の振込ステータス
        BulkTransferDetail bulkTransferDetailInfo = bulkTransferStatusResponse.getBulkTransferDetails().get(0);
        
        String bulktransferStatus = bulkTransferDetailInfo.getTransferStatus();
        
        List<FiatWithdrawal> fiatWithdrawalsByApplyNo =
            fiatWithdrawalService.findAllByApplyNo(applyNo);
        if (BulktransferStatus.PROCESSED.equals(bulktransferStatus)) {
          // 出金完了
          List<BulkTransferInfo> bulkTransferInfoList =
              bulkTransferDetailInfo.getBulktransferResponses().get(0).getBulkTransferInfos();
          updateByItemId(applyNo, bulkTransferInfoList);
        } else if (BulktransferStatus.ABANDON.equals(bulktransferStatus)
            || BulktransferStatus.APPROVE_CANCEL.equals(bulktransferStatus)
            || BulktransferStatus.EXPIRED.equals(bulktransferStatus)
            || BulktransferStatus.RESTITUTION.equals(bulktransferStatus)) {
          // (3:差戻、4:取下げ、5:期限切れ、8:承認取消/予約取消)
          // 出金エラー
          for (FiatWithdrawal errorInfo : fiatWithdrawalsByApplyNo) {
            fiatWithdrawalService.transferError(errorInfo.getId());
          }
        } else if (BulktransferStatus.PROCESS_ERROR.equals(bulktransferStatus)
            || BulktransferStatus.SETBACK.equals(bulktransferStatus)) {
          // (30:不能・組戻あり、40:手続不成立)
          // 不能明細情報を繰り返して、状態を更新する
          List<BulkTransferInfo> bulkTransferInfoList =
              bulkTransferDetailInfo.getBulktransferResponses().get(0).getBulkTransferInfos();
          updateByItemId(applyNo, bulkTransferInfoList);

        } else {
          // 待機中を更新
          for (FiatWithdrawal applyInfo : fiatWithdrawalsByApplyNo) {
            fiatWithdrawalService.applyWaitting(applyInfo.getId());
          }
        }

      } else if ("2".equals(resultCode)) {
        // 状態更新待ち、何もしない
      }
    }

  }

  private void updateByItemId(String applyNo, List<BulkTransferInfo> bulkTransferInfoList)
      throws Exception {
    for (BulkTransferInfo bulkTransferInfo : bulkTransferInfoList) {
      String itemId = bulkTransferInfo.getItemId();
      if (bulkTransferInfo.getUnableDetailInfos() != null) {
        if (bulkTransferInfo.getUnableDetailInfos().get(0) != null && !StringUtil.isNullOrEmpty(
            bulkTransferInfo.getUnableDetailInfos().get(0).getTransferDetailStatus())) {
//          String transferDetailStatus =
//              bulkTransferInfo.getUnableDetailInfos().get(0).getTransferDetailStatus();
          Boolean isRepayment = bulkTransferInfo.getUnableDetailInfos().get(0).getIsRepayment();
          
          String repaymentDateStr =
              bulkTransferInfo.getUnableDetailInfos().get(0).getRepaymentDate();
          if (!StringUtil.isNullOrEmpty(repaymentDateStr)) {
            String todayDateStr =
                FormatUtil.formatJst(new Date(), FormatPattern.YYYY_MM_DD);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Date repaymentDate = formatter.parse(repaymentDateStr);
            Date today = formatter.parse(todayDateStr);
            log.info("applyNo---itemId：　" + applyNo + "---" + itemId + "→　" + "資金返却日：　" + repaymentDate + "　今日：　" + today);
            if ((isRepayment != null && isRepayment) && !repaymentDate.after(today)) {
              // 資金返却日後
              // 出金エラー
              fiatWithdrawalService.transferErrorByItemId(applyNo, itemId);
            } else {
              // 資金返却日以前
              // 待機中
              fiatWithdrawalService.applyWaittingByItemId(applyNo, itemId);
            }
          } else {
            // 待機中
            fiatWithdrawalService.applyWaittingByItemId(applyNo, itemId);
          }
          
        } else {
          // 待機中
          fiatWithdrawalService.applyWaittingByItemId(applyNo, itemId);
        }

      } else {
        // 出金完了
        FiatWithdrawal relFiatWithdrawal =
            fiatWithdrawalService.applyGMOWithDrawalByItemId(applyNo, itemId);
        // 出金完了のお知らせを送信
        if (relFiatWithdrawal != null) {
          sendFiatWithdrawalSuccessMail(relFiatWithdrawal);
        }
      }

    }

  }

  /**
   * 出金完了メール送信
   * @param relFiatWithdrawal
   * @throws Exception
   */
  private void sendFiatWithdrawalSuccessMail(FiatWithdrawal relFiatWithdrawal) throws Exception {
    MailNoreply mailNoreply = mailNoreplyService.findOne(MailNoreplyType.FIAT_WITHDRAWAL_DONE);
    Asset afterAsset = assetService.findOne(relFiatWithdrawal.getUserId(), Currency.JPY);
    NumberFormat numberFormat = NumberFormat.getNumberInstance();
    String amountStr = relFiatWithdrawal.getAmount().stripTrailingZeros().toPlainString();
    String onhandAmountStr = afterAsset.getOnhandAmount().stripTrailingZeros().toPlainString();
    String content =
        formatContent(mailNoreply.getContents(), numberFormat.format(Long.valueOf(amountStr)),
            numberFormat.format(Long.valueOf(onhandAmountStr)));
    boolean send = sesManager.send(mailNoreply.getFromAddress(),
        relFiatWithdrawal.getUser().getEmail(), mailNoreply.getTitle(), content);
    if (!send) {
      log.info("FiatWithdrawal mail failed: " + relFiatWithdrawal.getUser().getEmail());
      throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
    }

  }

  private String formatContent(String content, String... arg) {
    MessageFormat format = new MessageFormat(content);
    return format.format(arg);
  }
}

package exchange.worker.worker;

import java.util.Date;
import java.util.Map;
import org.springframework.stereotype.Component;
import exchange.worker.component.Worker;
import exchange.common.constant.TradeType;
import exchange.common.entity.Symbol;
import exchange.common.service.ExchangeSummaryService;
import exchange.common.util.DateUnit;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class ExchangeSummaryCalculator extends Worker {

  private final ExchangeSummaryService exchangeSummaryService;

  /** ◆運用方法 */
  // JST 10/15 00:05起動分がエラー
  // 対応①事象解消後、当日中に再起動（param指定なし)
  // 対応②当日対応不可の場合、日次処理を停止し、param指定ありで再起動 => 完了後、param指定なしの日次処理再開

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    try {
      executeExchangeSummary(params);
    } catch (Exception e) {
      log.warn("ExchangeSummaryLog,error msg," + e);
    } finally {
      log.info("ExchangeSummaryLog,end");
    }
  }

  private void executeExchangeSummary(Map<String, Object> params) throws Exception {
    // デバッグ用パラメータ設定(dev環境ではAWSから)
    //    Map<String, Object> params = new HashMap<>();
    //    params.put("targetDayFrom", "20211014");
    //    params.put("targetDayTo", "20211014");

    log.info("DBMITO,ExchangeSummaryLog,params," + JsonUtil.encode(params));

    if (params.get("targetDayFrom") == null) {
      /** ①日次処理(params指定なし) */
      exchangeSummaryMake(params);
    } else {
      /** ②再作成処理(params指定あり) */
      // JST 10/14 00:00〜 10/16 23:59まで集計したい場合、param : targetDayFrom = 20211014, targetDayTo =
      exchangeSummaryReMake(params);
    }
  }

  /** ①日次処理(params指定なし) */
  private void exchangeSummaryMake(Map<String, Object> params) throws Exception {
    // 起動日時の取得
    // JST 10/15 00:03起動の場合、Thu Oct 14 15:03:11 GMT 2021
    Date today = new Date();

    // 前日の取得
    // Wed Oct 13 15:03:11 GMT 2021
    Date yesterday = new Date(today.getTime() - DateUnit.DAY.getMillis());

    // 前日をJSTに変換後、yyyymmdd形式で文字列取得
    // 20211014
    String targetDay = FormatUtil.formatJst(yesterday, FormatPattern.YYYYMMDD);

    // 前日JST yyyymmdd 00:00を UTCに変換
    // Wed Oct 13 15:00:00 GMT 2021
    Date targetFrom = FormatUtil.parseJst(targetDay, FormatPattern.YYYYMMDD);

    exchangeSummaryService.create(targetFrom, TradeType.SPOT);
    exchangeSummaryService.create(targetFrom, TradeType.POS);

    log.info(
        "DBMITO,ExchangeSummaryLog,today,"
            + today
            + ",yesterday,"
            + yesterday
            + ",targetDay,"
            + targetDay
            + ",targetFrom,"
            + targetFrom);
  }

  /** ②再作成処理(params指定あり) */
  private void exchangeSummaryReMake(Map<String, Object> params) throws Exception {
    // JST 10/14 00:00〜 10/16 23:59まで集計したい場合、param : targetDayFrom = 20211014, targetDayTo =
    // 20211016
    String targetDayFrom = (String) params.get("targetDayFrom");
    String targetDayTo = (String) params.get("targetDayTo");

    // JST yyyymmdd 00:00を UTCに変換
    //   from : Wed Oct 13 15:00:00 GMT 2021
    //   to   : Fri Oct 15 15:00:00 GMT 2021
    Date targetFrom = FormatUtil.parseJst(targetDayFrom, FormatPattern.YYYYMMDD);
    Date targetTo = FormatUtil.parseJst(targetDayTo, FormatPattern.YYYYMMDD);
    log.info(
        "DBMITO,ExchangeSummaryLog,targetDayFrom,"
            + targetDayFrom
            + ",targetFrom,"
            + targetFrom
            + ",targetDayTo,"
            + targetDayTo
            + ",targetTo,"
            + targetTo);

    // 集計終了日の翌日を取得(JST 00:00基準のUTC変換後Date)
    // toNext : Thu Oct 16 15:00:00 GMT 2021
    Date targetToNext = new Date(targetTo.getTime() + DateUnit.DAY.getMillis());

    while (targetFrom.before(targetToNext)) {

      //        exchangeSummaryService.create(targetFrom, TradeType.SPOT);
      exchangeSummaryService.create(targetFrom, TradeType.SPOT);
      exchangeSummaryService.create(targetFrom, TradeType.POS);

      // 翌日の集計開始日を取得
      // 例) 13,14,15実施後、16でtoNextと一致するため処理せず抜ける
      targetFrom = new Date(targetFrom.getTime() + DateUnit.DAY.getMillis());

      log.info(
          "DBMITO,ExchangeSummaryLog,targetDayFrom,"
              + targetDayFrom
              + ",targetFrom(nextDay),"
              + targetFrom
              + ",targetToNext,"
              + targetToNext);
    }
  }
}
